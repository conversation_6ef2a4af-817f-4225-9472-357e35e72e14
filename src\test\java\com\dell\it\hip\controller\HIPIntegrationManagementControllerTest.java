package com.dell.it.hip.controller;

import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.config.IntegrationStatus;
import com.dell.it.hip.controller.dto.IntegrationDefinitionsWithStatusResponse;
import com.dell.it.hip.core.HIPIntegrationOrchestrationService;
import com.dell.it.hip.core.HIPIntegrationRuntimeService;
import com.dell.it.hip.core.ServiceManager;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * Unit tests for HIPIntegrationManagementController definition retrieval methods.
 * Tests both single definition retrieval and multi-version definition retrieval.
 */
@ExtendWith(MockitoExtension.class)
public class HIPIntegrationManagementControllerTest {

    private static final String SERVICE_MANAGER_NAME = "test-service-manager";

    @Mock
    private HIPIntegrationOrchestrationService orchestrationService;

    @Mock
    private ServiceManager serviceManager;

    @Mock
    private HIPIntegrationRuntimeService hipIntegrationRuntimeService;

    @InjectMocks
    private HIPIntegrationManagementController controller;

    private HIPIntegrationDefinition testDefinitionV1;
    private HIPIntegrationDefinition testDefinitionV2;
    private HIPIntegrationDefinition testDefinitionV3;

    private HIPIntegrationOrchestrationService.HIPIntegrationInfo testInfoV1;
    private HIPIntegrationOrchestrationService.HIPIntegrationInfo testInfoV2;
    private HIPIntegrationOrchestrationService.HIPIntegrationInfo testInfoV3;

    @BeforeEach
    void setUp() {
        // Reset mocks to ensure clean state for each test
        reset(serviceManager, orchestrationService, hipIntegrationRuntimeService);

        // Set the service manager name using reflection
        ReflectionTestUtils.setField(controller, "serviceManagerName", SERVICE_MANAGER_NAME);

        // Create test definitions for different versions of the same integration
        testDefinitionV1 = createTestDefinition("test-integration", "1.0");
        testDefinitionV2 = createTestDefinition("test-integration", "2.0");
        testDefinitionV3 = createTestDefinition("test-integration", "3.0");

        // Create test integration info objects with status
        testInfoV1 = new HIPIntegrationOrchestrationService.HIPIntegrationInfo("test-integration", "1.0", IntegrationStatus.RUNNING);
        testInfoV2 = new HIPIntegrationOrchestrationService.HIPIntegrationInfo("test-integration", "2.0", IntegrationStatus.PAUSED);
        testInfoV3 = new HIPIntegrationOrchestrationService.HIPIntegrationInfo("test-integration", "3.0", IntegrationStatus.RUNNING);
    }

    // === Tests for existing getDefinition method ===

    @Test
    void testGetDefinition_Success() {
        // Arrange
        when(serviceManager.getIntegrationDefinition("test-integration", "1.0"))
                .thenReturn(testDefinitionV1);

        // Act
        ResponseEntity<?> response = controller.getDefinition("test-integration", "1.0");

        // Assert
        assertEquals(200, response.getStatusCode().value());
        assertNotNull(response.getBody());
        assertTrue(response.getBody() instanceof HIPIntegrationDefinition);
        HIPIntegrationDefinition returnedDef = (HIPIntegrationDefinition) response.getBody();
        assertEquals("test-integration", returnedDef.getHipIntegrationName());
        assertEquals("1.0", returnedDef.getVersion());
        
        verify(serviceManager).getIntegrationDefinition("test-integration", "1.0");
    }

    @Test
    void testGetDefinition_NotFound() {
        // Arrange
        when(serviceManager.getIntegrationDefinition("non-existent", "1.0"))
                .thenReturn(null);

        // Act
        ResponseEntity<?> response = controller.getDefinition("non-existent", "1.0");

        // Assert
        assertEquals(404, response.getStatusCode().value());
        verify(serviceManager).getIntegrationDefinition("non-existent", "1.0");
    }

    // === Tests for new getDefinitionByName method ===

    @Test
    void testGetDefinitionByName_Success_MultipleVersions() {
        // Arrange
        List<HIPIntegrationDefinition> expectedDefinitions = Arrays.asList(
                testDefinitionV1, testDefinitionV2, testDefinitionV3);

        when(serviceManager.getDefinitionsByName("test-integration"))
                .thenReturn(expectedDefinitions);
        when(hipIntegrationRuntimeService.getHIPIntegrationStatus(SERVICE_MANAGER_NAME, "test-integration", "1.0"))
                .thenReturn(IntegrationStatus.RUNNING);
        when(hipIntegrationRuntimeService.getHIPIntegrationStatus(SERVICE_MANAGER_NAME, "test-integration", "2.0"))
                .thenReturn(IntegrationStatus.PAUSED);
        when(hipIntegrationRuntimeService.getHIPIntegrationStatus(SERVICE_MANAGER_NAME, "test-integration", "3.0"))
                .thenReturn(IntegrationStatus.RUNNING);

        // Act
        ResponseEntity<IntegrationDefinitionsWithStatusResponse> response = controller.getDefinitionByName("test-integration");

        // Assert
        assertEquals(200, response.getStatusCode().value());
        assertNotNull(response.getBody());
        assertTrue(response.getBody() instanceof IntegrationDefinitionsWithStatusResponse);

        IntegrationDefinitionsWithStatusResponse responseBody = response.getBody();

        // Verify definitions
        List<HIPIntegrationDefinition> returnedDefinitions = responseBody.getDefinitions();
        assertNotNull(returnedDefinitions);
        assertEquals(3, returnedDefinitions.size());

        // Verify all definitions have the same integration name
        returnedDefinitions.forEach(def ->
                assertEquals("test-integration", def.getHipIntegrationName()));

        // Verify different versions are present
        assertTrue(returnedDefinitions.stream().anyMatch(def -> "1.0".equals(def.getVersion())));
        assertTrue(returnedDefinitions.stream().anyMatch(def -> "2.0".equals(def.getVersion())));
        assertTrue(returnedDefinitions.stream().anyMatch(def -> "3.0".equals(def.getVersion())));

        // Verify status information
        Map<String, IntegrationStatus> statusMap = responseBody.getStatus();
        assertNotNull(statusMap);
        assertEquals(3, statusMap.size());
        assertEquals(IntegrationStatus.RUNNING, statusMap.get("1.0"));
        assertEquals(IntegrationStatus.PAUSED, statusMap.get("2.0"));
        assertEquals(IntegrationStatus.RUNNING, statusMap.get("3.0"));

        // Verify service interactions - exactly one call each
        verify(serviceManager, times(1)).getDefinitionsByName("test-integration");
        verify(hipIntegrationRuntimeService, times(1)).getHIPIntegrationStatus(SERVICE_MANAGER_NAME, "test-integration", "1.0");
        verify(hipIntegrationRuntimeService, times(1)).getHIPIntegrationStatus(SERVICE_MANAGER_NAME, "test-integration", "2.0");
        verify(hipIntegrationRuntimeService, times(1)).getHIPIntegrationStatus(SERVICE_MANAGER_NAME, "test-integration", "3.0");

        // Ensure no other interactions occurred
        verifyNoMoreInteractions(serviceManager, hipIntegrationRuntimeService);
    }

    @Test
    void testGetDefinitionByName_Success_SingleVersion() {
        // Arrange
        List<HIPIntegrationDefinition> expectedDefinitions = Collections.singletonList(testDefinitionV1);

        when(serviceManager.getDefinitionsByName("single-version-integration"))
                .thenReturn(expectedDefinitions);
        when(hipIntegrationRuntimeService.getHIPIntegrationStatus(SERVICE_MANAGER_NAME, "test-integration", "1.0"))
                .thenReturn(IntegrationStatus.RUNNING);

        // Act
        ResponseEntity<IntegrationDefinitionsWithStatusResponse> response = controller.getDefinitionByName("single-version-integration");

        // Assert
        assertEquals(200, response.getStatusCode().value());
        assertNotNull(response.getBody());
        assertTrue(response.getBody() instanceof IntegrationDefinitionsWithStatusResponse);

        IntegrationDefinitionsWithStatusResponse responseBody = response.getBody();

        // Verify definitions
        List<HIPIntegrationDefinition> returnedDefinitions = responseBody.getDefinitions();
        assertNotNull(returnedDefinitions);
        assertEquals(1, returnedDefinitions.size());
        assertEquals("test-integration", returnedDefinitions.get(0).getHipIntegrationName());
        assertEquals("1.0", returnedDefinitions.get(0).getVersion());

        // Verify status information
        Map<String, IntegrationStatus> statusMap = responseBody.getStatus();
        assertNotNull(statusMap);
        assertEquals(1, statusMap.size());
        assertEquals(IntegrationStatus.RUNNING, statusMap.get("1.0"));

        // Verify service interactions - exactly one call each
        verify(serviceManager, times(1)).getDefinitionsByName("single-version-integration");
        verify(hipIntegrationRuntimeService, times(1)).getHIPIntegrationStatus(SERVICE_MANAGER_NAME, "test-integration", "1.0");

        // Ensure no other interactions occurred
        verifyNoMoreInteractions(serviceManager, hipIntegrationRuntimeService);
    }

    @Test
    void testGetDefinitionByName_NotFound_EmptyList() {
        // Arrange
        when(serviceManager.getDefinitionsByName("non-existent-integration"))
                .thenReturn(Collections.emptyList());

        // Act
        ResponseEntity<IntegrationDefinitionsWithStatusResponse> response = controller.getDefinitionByName("non-existent-integration");

        // Assert
        assertEquals(404, response.getStatusCode().value());
        verify(serviceManager).getDefinitionsByName("non-existent-integration");
        // hipIntegrationRuntimeService should not be called when no definitions are found
        verifyNoInteractions(hipIntegrationRuntimeService);
    }

    @Test
    void testGetDefinitionByName_WithNullName() {
        // Arrange
        when(serviceManager.getDefinitionsByName(null))
                .thenReturn(Collections.emptyList());

        // Act
        ResponseEntity<IntegrationDefinitionsWithStatusResponse> response = controller.getDefinitionByName(null);

        // Assert
        assertEquals(404, response.getStatusCode().value());
        verify(serviceManager).getDefinitionsByName(null);
        // hipIntegrationRuntimeService should not be called when no definitions are found
        verifyNoInteractions(hipIntegrationRuntimeService);
    }

    @Test
    void testGetDefinitionByName_WithEmptyName() {
        // Arrange
        when(serviceManager.getDefinitionsByName(""))
                .thenReturn(Collections.emptyList());

        // Act
        ResponseEntity<IntegrationDefinitionsWithStatusResponse> response = controller.getDefinitionByName("");

        // Assert
        assertEquals(404, response.getStatusCode().value());
        verify(serviceManager).getDefinitionsByName("");
        // hipIntegrationRuntimeService should not be called when no definitions are found
        verifyNoInteractions(hipIntegrationRuntimeService);
    }

    @Test
    void testGetDefinitionByName_WithSpecialCharacters() {
        // Arrange
        String specialName = "test-integration-with-special-chars_123";
        HIPIntegrationDefinition specialDef = createTestDefinition(specialName, "1.0");

        when(serviceManager.getDefinitionsByName(specialName))
                .thenReturn(Collections.singletonList(specialDef));
        when(hipIntegrationRuntimeService.getHIPIntegrationStatus(SERVICE_MANAGER_NAME, specialName, "1.0"))
                .thenReturn(IntegrationStatus.RUNNING);

        // Act
        ResponseEntity<IntegrationDefinitionsWithStatusResponse> response = controller.getDefinitionByName(specialName);

        // Assert
        assertEquals(200, response.getStatusCode().value());
        assertNotNull(response.getBody());
        assertTrue(response.getBody() instanceof IntegrationDefinitionsWithStatusResponse);

        IntegrationDefinitionsWithStatusResponse responseBody = response.getBody();

        // Verify definitions
        List<HIPIntegrationDefinition> returnedDefinitions = responseBody.getDefinitions();
        assertNotNull(returnedDefinitions);
        assertEquals(1, returnedDefinitions.size());
        assertEquals(specialName, returnedDefinitions.get(0).getHipIntegrationName());

        // Verify status information
        Map<String, IntegrationStatus> statusMap = responseBody.getStatus();
        assertNotNull(statusMap);
        assertEquals(1, statusMap.size());
        assertEquals(IntegrationStatus.RUNNING, statusMap.get("1.0"));

        verify(serviceManager, times(1)).getDefinitionsByName(specialName);
        verify(hipIntegrationRuntimeService, times(1)).getHIPIntegrationStatus(SERVICE_MANAGER_NAME, specialName, "1.0");
        verifyNoMoreInteractions(serviceManager, hipIntegrationRuntimeService);
    }

    // === Additional tests for status data validation ===

    @Test
    void testGetDefinitionByName_StatusDataStructure() {
        // Arrange
        List<HIPIntegrationDefinition> expectedDefinitions = Arrays.asList(testDefinitionV1, testDefinitionV2);

        when(serviceManager.getDefinitionsByName("test-integration"))
                .thenReturn(expectedDefinitions);
        when(hipIntegrationRuntimeService.getHIPIntegrationStatus(SERVICE_MANAGER_NAME, "test-integration", "1.0"))
                .thenReturn(IntegrationStatus.RUNNING);
        when(hipIntegrationRuntimeService.getHIPIntegrationStatus(SERVICE_MANAGER_NAME, "test-integration", "2.0"))
                .thenReturn(IntegrationStatus.PAUSED);

        // Act
        ResponseEntity<IntegrationDefinitionsWithStatusResponse> response = controller.getDefinitionByName("test-integration");

        // Assert
        assertEquals(200, response.getStatusCode().value());
        IntegrationDefinitionsWithStatusResponse responseBody = response.getBody();
        assertNotNull(responseBody);

        // Verify response structure
        assertNotNull(responseBody.getDefinitions());
        assertNotNull(responseBody.getStatus());

        // Verify status map contains correct keys and values
        Map<String, IntegrationStatus> statusMap = responseBody.getStatus();
        assertTrue(statusMap.containsKey("1.0"));
        assertTrue(statusMap.containsKey("2.0"));
        assertEquals(IntegrationStatus.RUNNING, statusMap.get("1.0"));
        assertEquals(IntegrationStatus.PAUSED, statusMap.get("2.0"));

        verify(serviceManager, times(1)).getDefinitionsByName("test-integration");
        verify(hipIntegrationRuntimeService, times(1)).getHIPIntegrationStatus(SERVICE_MANAGER_NAME, "test-integration", "1.0");
        verify(hipIntegrationRuntimeService, times(1)).getHIPIntegrationStatus(SERVICE_MANAGER_NAME, "test-integration", "2.0");
        verifyNoMoreInteractions(serviceManager, hipIntegrationRuntimeService);
    }

    @Test
    void testGetDefinitionByName_MixedStatusValues() {
        // Arrange - Test all possible status values
        HIPIntegrationDefinition defError = createTestDefinition("test-integration", "4.0");
        HIPIntegrationDefinition defUnregistered = createTestDefinition("test-integration", "5.0");

        List<HIPIntegrationDefinition> expectedDefinitions = Arrays.asList(
                testDefinitionV1, testDefinitionV2, testDefinitionV3, defError, defUnregistered);

        when(serviceManager.getDefinitionsByName("test-integration"))
                .thenReturn(expectedDefinitions);
        when(hipIntegrationRuntimeService.getHIPIntegrationStatus(SERVICE_MANAGER_NAME, "test-integration", "1.0"))
                .thenReturn(IntegrationStatus.RUNNING);
        when(hipIntegrationRuntimeService.getHIPIntegrationStatus(SERVICE_MANAGER_NAME, "test-integration", "2.0"))
                .thenReturn(IntegrationStatus.PAUSED);
        when(hipIntegrationRuntimeService.getHIPIntegrationStatus(SERVICE_MANAGER_NAME, "test-integration", "3.0"))
                .thenReturn(IntegrationStatus.RUNNING);
        when(hipIntegrationRuntimeService.getHIPIntegrationStatus(SERVICE_MANAGER_NAME, "test-integration", "4.0"))
                .thenReturn(IntegrationStatus.ERROR);
        when(hipIntegrationRuntimeService.getHIPIntegrationStatus(SERVICE_MANAGER_NAME, "test-integration", "5.0"))
                .thenReturn(IntegrationStatus.UNREGISTERED);

        // Act
        ResponseEntity<IntegrationDefinitionsWithStatusResponse> response = controller.getDefinitionByName("test-integration");

        // Assert
        assertEquals(200, response.getStatusCode().value());
        IntegrationDefinitionsWithStatusResponse responseBody = response.getBody();
        assertNotNull(responseBody);

        Map<String, IntegrationStatus> statusMap = responseBody.getStatus();
        assertEquals(5, statusMap.size());
        assertEquals(IntegrationStatus.RUNNING, statusMap.get("1.0"));
        assertEquals(IntegrationStatus.PAUSED, statusMap.get("2.0"));
        assertEquals(IntegrationStatus.RUNNING, statusMap.get("3.0"));
        assertEquals(IntegrationStatus.ERROR, statusMap.get("4.0"));
        assertEquals(IntegrationStatus.UNREGISTERED, statusMap.get("5.0"));

        verify(serviceManager, times(1)).getDefinitionsByName("test-integration");
        verify(hipIntegrationRuntimeService, times(1)).getHIPIntegrationStatus(SERVICE_MANAGER_NAME, "test-integration", "1.0");
        verify(hipIntegrationRuntimeService, times(1)).getHIPIntegrationStatus(SERVICE_MANAGER_NAME, "test-integration", "2.0");
        verify(hipIntegrationRuntimeService, times(1)).getHIPIntegrationStatus(SERVICE_MANAGER_NAME, "test-integration", "3.0");
        verify(hipIntegrationRuntimeService, times(1)).getHIPIntegrationStatus(SERVICE_MANAGER_NAME, "test-integration", "4.0");
        verify(hipIntegrationRuntimeService, times(1)).getHIPIntegrationStatus(SERVICE_MANAGER_NAME, "test-integration", "5.0");
        verifyNoMoreInteractions(serviceManager, hipIntegrationRuntimeService);
    }

    @Test
    void testGetDefinitionByName_StatusNotFoundDefaultsToUnregistered() {
        // Arrange - Definition exists but no status info available in Redis
        List<HIPIntegrationDefinition> expectedDefinitions = Collections.singletonList(testDefinitionV1);

        when(serviceManager.getDefinitionsByName("test-integration"))
                .thenReturn(expectedDefinitions);
        when(hipIntegrationRuntimeService.getHIPIntegrationStatus(SERVICE_MANAGER_NAME, "test-integration", "1.0"))
                .thenReturn(IntegrationStatus.UNREGISTERED); // Default when Redis key doesn't exist

        // Act
        ResponseEntity<IntegrationDefinitionsWithStatusResponse> response = controller.getDefinitionByName("test-integration");

        // Assert
        assertEquals(200, response.getStatusCode().value());
        IntegrationDefinitionsWithStatusResponse responseBody = response.getBody();
        assertNotNull(responseBody);

        Map<String, IntegrationStatus> statusMap = responseBody.getStatus();
        assertEquals(1, statusMap.size());
        assertEquals(IntegrationStatus.UNREGISTERED, statusMap.get("1.0"));

        verify(serviceManager, times(1)).getDefinitionsByName("test-integration");
        verify(hipIntegrationRuntimeService, times(1)).getHIPIntegrationStatus(SERVICE_MANAGER_NAME, "test-integration", "1.0");
        verifyNoMoreInteractions(serviceManager, hipIntegrationRuntimeService);
    }

    // === Backward compatibility tests ===

    @Test
    void testSeparateStatusEndpoint_StillWorks() {
        // Arrange
        List<HIPIntegrationOrchestrationService.HIPIntegrationInfo> expectedInfos = Arrays.asList(
                testInfoV1, testInfoV2, testInfoV3);

        when(orchestrationService.getAllHIPIntegrationsWithStatus())
                .thenReturn(expectedInfos);

        // Act
        ResponseEntity<String> response = controller.status("test-integration", "2.0");

        // Assert
        assertEquals(200, response.getStatusCode().value());
        assertEquals("PAUSED", response.getBody());
        assertEquals(MediaType.TEXT_PLAIN, response.getHeaders().getContentType());

        verify(orchestrationService, times(1)).getAllHIPIntegrationsWithStatus();
        verifyNoMoreInteractions(orchestrationService);
    }

    @Test
    void testSeparateStatusEndpoint_NotFound() {
        // Arrange
        List<HIPIntegrationOrchestrationService.HIPIntegrationInfo> expectedInfos = Arrays.asList(testInfoV1);

        when(orchestrationService.getAllHIPIntegrationsWithStatus())
                .thenReturn(expectedInfos);

        // Act
        ResponseEntity<String> response = controller.status("non-existent", "1.0");

        // Assert
        assertEquals(200, response.getStatusCode().value());
        assertEquals("UNREGISTERED", response.getBody());
        assertEquals(MediaType.TEXT_PLAIN, response.getHeaders().getContentType());

        verify(orchestrationService, times(1)).getAllHIPIntegrationsWithStatus();
        verifyNoMoreInteractions(orchestrationService);
    }

    @Test
    void testSingleDefinitionEndpoint_StillWorks() {
        // Arrange
        when(serviceManager.getIntegrationDefinition("test-integration", "1.0"))
                .thenReturn(testDefinitionV1);

        // Act
        ResponseEntity<?> response = controller.getDefinition("test-integration", "1.0");

        // Assert
        assertEquals(200, response.getStatusCode().value());
        assertNotNull(response.getBody());
        assertTrue(response.getBody() instanceof HIPIntegrationDefinition);
        HIPIntegrationDefinition returnedDef = (HIPIntegrationDefinition) response.getBody();
        assertEquals("test-integration", returnedDef.getHipIntegrationName());
        assertEquals("1.0", returnedDef.getVersion());

        verify(serviceManager).getIntegrationDefinition("test-integration", "1.0");
    }

    // === Helper methods ===

    private HIPIntegrationDefinition createTestDefinition(String name, String version) {
        HIPIntegrationDefinition definition = new HIPIntegrationDefinition();
        definition.setHipIntegrationName(name);
        definition.setVersion(version);
        definition.setServiceManagerName("test-service-manager");
        definition.setBusinessFlowName("test-flow");
        definition.setDescription("Test integration definition for " + name + " v" + version);
        definition.setOwner("test-team");
        return definition;
    }
}
